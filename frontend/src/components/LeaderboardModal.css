.leaderboard-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
}

.leaderboard-modal-container {
  background: white;
  border-radius: 16px;
  width: 50%;
  max-width: 800px;
  max-height: 90vh;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  position: relative;
  transform: scale(0.9);
  animation: modalSlideIn 0.3s ease-out forwards;
}

@keyframes modalSlideIn {
  to {
    transform: scale(1);
  }
}

.leaderboard-modal-header {
  background: linear-gradient(135deg, #FF9800, #FF5722);
  color: white;
  padding: 20px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.leaderboard-modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
}

.leaderboard-modal-close-button {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.leaderboard-modal-close-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.leaderboard-modal-content {
  padding: 0px;
  max-height: calc(90vh - 100px);
  overflow-y: auto;
}

/* Loading and error states */
.leaderboard-loading,
.leaderboard-error,
.leaderboard-no-data {
  text-align: center;
  padding: 40px 20px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #FF9800;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

.leaderboard-error {
  color: #f44336;
}

.leaderboard-no-data {
  color: #666;
}

/* Table styles */
.leaderboard-table-container {
  overflow-x: auto;
}

.leaderboard-table {
  width: 100%;
  border-collapse: collapse;
}

.leaderboard-table th {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  color: #333;
  font-weight: 600;
  padding: 15px 12px;
  text-align: left;
  border-bottom: 2px solid #dee2e6;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
}

.info-icon {
  font-size: 0.8rem;
  cursor: help;
  opacity: 0.7;
  transition: opacity 0.2s ease;
  margin-left: 4px;
}

.info-icon:hover {
  opacity: 1;
}

.leaderboard-table td {
  padding: 12px;
  border-bottom: 1px solid #e9ecef;
  vertical-align: middle;
}

.leaderboard-table tbody tr:hover {
  background-color: #f8f9fa;
  transition: background-color 0.2s ease;
}

.leaderboard-table tbody tr.leaderboard-row {
  cursor: pointer;
}

.leaderboard-table tbody tr.leaderboard-row:hover {
  background-color: rgba(0, 123, 255, 0.1);
}

.placement-cell {
  font-weight: 700;
  color: #FF9800;
  text-align: center;
  width: 80px;
}

.rank-cell {
  text-align: center;
  width: 80px;
}

.rank-badge {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 6px;
  font-weight: 600;
  font-size: 0.9rem;
  min-width: 40px;
  text-align: center;
}

.nickname-cell {
  font-weight: 600;
  color: #333;
}

.winrate-cell {
  text-align: center;
  font-weight: 500;
  width: 100px;
}

.totalgames-cell {
  text-align: center;
  font-weight: 500;
  width: 100px;
}

.percentile-cell {
  text-align: center;
  font-weight: 500;
  color: #666;
  width: 100px;
}

/* Status change indicators */
.status-arrow {
  margin-left: 8px;
  margin-right: 8px;
  font-size: 1.1rem;
  display: inline-block;
}

.status-arrow.promote {
  color: #4caf50;
}

.status-arrow.demote {
  color: #f44336;
}

/* Row highlighting for status changes */
.status-promote {
  background-color: rgba(76, 175, 80, 0.05);
  border-left: 3px solid #4caf50;
}

.status-demote {
  background-color: rgba(244, 67, 54, 0.05);
  border-left: 3px solid #f44336;
}

/* N/A value styling */
.na-value {
  color: #999;
  font-style: italic;
  opacity: 0.7;
}

/* Responsive design */
@media (max-width: 768px) {
  .leaderboard-modal-container {
    width: 95%;
    max-height: 95vh;
  }

  .leaderboard-modal-header {
    padding: 15px 20px;
  }

  .leaderboard-modal-header h2 {
    font-size: 1.3rem;
  }

  .leaderboard-modal-content {
    padding: 0px;
  }

  .leaderboard-table th,
  .leaderboard-table td {
    padding: 8px 6px;
    font-size: 0.85rem;
  }

  .rank-badge {
    padding: 4px 8px;
    font-size: 0.8rem;
  }
}

.info-header {
    flex-direction: row;
    align-items: center;
    gap: 6px;
}

@media (max-width: 480px) {
  .leaderboard-table th,
  .leaderboard-table td {
    padding: 6px 4px;
    font-size: 0.8rem;
  }

  .placement-cell,
  .rank-cell,
  .winrate-cell,
  .totalgames-cell,
  .percentile-cell {
    width: auto;
  }

  .info-icon {
    font-size: 0.7rem;
  }
}
