{"name": "team-balancer-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui"}, "dependencies": {"chart.js": "^4.4.0", "chartjs-plugin-annotation": "^3.1.0", "i18next": "^25.0.1", "i18next-browser-languagedetector": "^8.0.5", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-i18next": "^15.5.1"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "@vitest/coverage-v8": "^3.1.2", "@vitest/ui": "^3.1.2", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "jsdom": "^26.1.0", "vite": "^4.4.5", "vitest": "^3.1.2"}}